# Database Configuration
# Copy this file to .env and update with your actual database credentials

# Database Connection Settings
DB_URL=***************************************************************************************************
DB_USERNAME=root
DB_PASSWORD=your_database_password_here
DB_DRIVER=com.mysql.cj.jdbc.Driver

# Database Pool Settings (Optional)
DB_POOL_INITIAL_SIZE=5
DB_POOL_MAX_ACTIVE=20
DB_POOL_MAX_IDLE=10
DB_POOL_MIN_IDLE=5
DB_POOL_MAX_WAIT=30000

# Security Settings (Optional)
DB_CONNECTION_TIMEOUT=30000
DB_SOCKET_TIMEOUT=60000
DB_AUTO_RECONNECT=true
DB_FAIL_OVER_READ_ONLY=false

# Application Settings (Optional)
APP_NAME=Online Grocery Ordering System
APP_VERSION=1.0.0
APP_ENVIRONMENT=development

# Logging Settings (Optional)
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/application.log

# Session Settings (Optional)
SESSION_TIMEOUT_MINUTES=30
MAX_LOGIN_ATTEMPTS=3

# Admin Default Credentials (DO NOT CHANGE IN PRODUCTION)
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123

# Security Configuration (Optional)
ENABLE_SQL_INJECTION_DETECTION=true
ENABLE_INPUT_SANITIZATION=true
PASSWORD_MIN_LENGTH=8
CUSTOMER_ID_LENGTH=6

# Database Schema Settings (Optional)
AUTO_CREATE_TABLES=true
AUTO_INSERT_DEFAULT_DATA=true

# Performance Settings (Optional)
QUERY_TIMEOUT_SECONDS=30
CONNECTION_POOL_VALIDATION_QUERY=SELECT 1

# Development Settings (Optional)
DEBUG_MODE=false
ENABLE_DETAILED_LOGGING=false
SHOW_SQL_QUERIES=false
